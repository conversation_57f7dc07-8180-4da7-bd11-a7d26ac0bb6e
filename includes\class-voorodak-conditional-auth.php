<?php
/**
 * Voorodak Conditional Authentication System
 * 
 * Provides flexible authentication triggers and conditions
 * 
 * @package Voorodak
 * @version 3.0.2
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * Conditional Authentication Manager
 */
class Voorodak_Conditional_Auth
{
    use Voorodak_Options;
    
    /**
     * Authentication conditions
     * 
     * @var array
     */
    private $conditions = [];
    
    /**
     * Authentication triggers
     * 
     * @var array
     */
    private $triggers = [];
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->init_default_conditions();
        $this->init_hooks();
    }
    
    /**
     * Initialize default authentication conditions
     * 
     * @return void
     */
    private function init_default_conditions()
    {
        // Page-based conditions
        $this->add_condition('page_locked', [$this, 'check_page_locked']);
        $this->add_condition('woocommerce_account', [$this, 'check_woocommerce_account']);
        $this->add_condition('woocommerce_checkout', [$this, 'check_woocommerce_checkout']);
        
        // User-based conditions
        $this->add_condition('user_role', [$this, 'check_user_role']);
        $this->add_condition('user_capability', [$this, 'check_user_capability']);
        
        // Time-based conditions
        $this->add_condition('time_range', [$this, 'check_time_range']);
        $this->add_condition('date_range', [$this, 'check_date_range']);
        
        // Content-based conditions
        $this->add_condition('post_type', [$this, 'check_post_type']);
        $this->add_condition('taxonomy', [$this, 'check_taxonomy']);
        
        // Custom conditions via filter
        $this->conditions = apply_filters('voorodak_auth_conditions', $this->conditions);
    }
    
    /**
     * Initialize WordPress hooks
     * 
     * @return void
     */
    private function init_hooks()
    {
        add_action('template_redirect', [$this, 'evaluate_conditions'], 5);
        add_filter('voorodak_auth_required', [$this, 'filter_auth_required'], 10, 2);
        add_action('wp', [$this, 'process_triggers']);
    }
    
    /**
     * Add authentication condition
     * 
     * @param string $name Condition name
     * @param callable $callback Condition callback
     * @return void
     */
    public function add_condition($name, $callback)
    {
        $this->conditions[$name] = $callback;
    }
    
    /**
     * Add authentication trigger
     * 
     * @param string $name Trigger name
     * @param array $config Trigger configuration
     * @return void
     */
    public function add_trigger($name, $config)
    {
        $this->triggers[$name] = $config;
    }
    
    /**
     * Evaluate all authentication conditions
     * 
     * @return void
     */
    public function evaluate_conditions()
    {
        if (is_user_logged_in()) {
            return;
        }
        
        $settings = $this->get_settings();
        $login_page_id = $settings['login_page_id'] ?? '';
        
        // Skip if on login page
        if (is_page($login_page_id)) {
            return;
        }
        
        $auth_required = false;
        $redirect_url = '';
        $condition_met = '';
        
        foreach ($this->conditions as $name => $callback) {
            if (is_callable($callback)) {
                $result = call_user_func($callback);
                
                if (is_array($result) && $result['required']) {
                    $auth_required = true;
                    $redirect_url = $result['redirect_url'] ?? '';
                    $condition_met = $name;
                    break;
                }
            }
        }
        
        // Apply filter to allow override
        $auth_data = apply_filters('voorodak_conditional_auth_result', [
            'required' => $auth_required,
            'redirect_url' => $redirect_url,
            'condition' => $condition_met
        ]);
        
        if ($auth_data['required']) {
            $this->redirect_to_login($auth_data['redirect_url'], $auth_data['condition']);
        }
    }
    
    /**
     * Check if page is locked
     * 
     * @return array
     */
    public function check_page_locked()
    {
        global $post;
        
        if (!$post || !is_singular()) {
            return ['required' => false];
        }
        
        $locked = get_post_meta($post->ID, '_lock_voorodak', true);
        
        if ($locked) {
            return [
                'required' => true,
                'redirect_url' => get_the_permalink($post->ID)
            ];
        }
        
        return ['required' => false];
    }
    
    /**
     * Check WooCommerce account page
     * 
     * @return array
     */
    public function check_woocommerce_account()
    {
        if (!function_exists('is_woocommerce')) {
            return ['required' => false];
        }
        
        $settings = $this->get_settings();
        $woocommerce_login = $settings['woocommerce_login'] ?? '';
        
        if ($woocommerce_login && is_account_page()) {
            return ['required' => true];
        }
        
        return ['required' => false];
    }
    
    /**
     * Check WooCommerce checkout page
     * 
     * @return array
     */
    public function check_woocommerce_checkout()
    {
        if (!function_exists('is_woocommerce')) {
            return ['required' => false];
        }
        
        $settings = $this->get_settings();
        $woocommerce_checkout = $settings['woocommerce_checkout'] ?? '';
        
        if ($woocommerce_checkout && is_checkout()) {
            return [
                'required' => true,
                'redirect_url' => 'checkout'
            ];
        }
        
        return ['required' => false];
    }
    
    /**
     * Check user role requirement
     * 
     * @return array
     */
    public function check_user_role()
    {
        // This would be configured via settings or filters
        $required_roles = apply_filters('voorodak_required_roles', []);
        
        if (empty($required_roles)) {
            return ['required' => false];
        }
        
        // Check if current page requires specific roles
        global $post;
        if ($post) {
            $page_roles = get_post_meta($post->ID, '_voorodak_required_roles', true);
            if (!empty($page_roles)) {
                return ['required' => true];
            }
        }
        
        return ['required' => false];
    }
    
    /**
     * Check user capability requirement
     * 
     * @return array
     */
    public function check_user_capability()
    {
        global $post;
        
        if (!$post) {
            return ['required' => false];
        }
        
        $required_cap = get_post_meta($post->ID, '_voorodak_required_capability', true);
        
        if (!empty($required_cap)) {
            return ['required' => true];
        }
        
        return ['required' => false];
    }
    
    /**
     * Check time range requirement
     * 
     * @return array
     */
    public function check_time_range()
    {
        $time_restrictions = apply_filters('voorodak_time_restrictions', []);
        
        if (empty($time_restrictions)) {
            return ['required' => false];
        }
        
        $current_time = current_time('H:i');
        
        foreach ($time_restrictions as $restriction) {
            if (isset($restriction['start'], $restriction['end'])) {
                if ($current_time >= $restriction['start'] && $current_time <= $restriction['end']) {
                    return ['required' => true];
                }
            }
        }
        
        return ['required' => false];
    }
    
    /**
     * Check date range requirement
     * 
     * @return array
     */
    public function check_date_range()
    {
        $date_restrictions = apply_filters('voorodak_date_restrictions', []);
        
        if (empty($date_restrictions)) {
            return ['required' => false];
        }
        
        $current_date = current_time('Y-m-d');
        
        foreach ($date_restrictions as $restriction) {
            if (isset($restriction['start'], $restriction['end'])) {
                if ($current_date >= $restriction['start'] && $current_date <= $restriction['end']) {
                    return ['required' => true];
                }
            }
        }
        
        return ['required' => false];
    }
    
    /**
     * Check post type requirement
     * 
     * @return array
     */
    public function check_post_type()
    {
        $protected_post_types = apply_filters('voorodak_protected_post_types', []);
        
        if (empty($protected_post_types)) {
            return ['required' => false];
        }
        
        $current_post_type = get_post_type();
        
        if (in_array($current_post_type, $protected_post_types, true)) {
            return ['required' => true];
        }
        
        return ['required' => false];
    }
    
    /**
     * Check taxonomy requirement
     * 
     * @return array
     */
    public function check_taxonomy()
    {
        $protected_terms = apply_filters('voorodak_protected_terms', []);
        
        if (empty($protected_terms)) {
            return ['required' => false];
        }
        
        global $post;
        if (!$post) {
            return ['required' => false];
        }
        
        foreach ($protected_terms as $taxonomy => $terms) {
            if (has_term($terms, $taxonomy, $post)) {
                return ['required' => true];
            }
        }
        
        return ['required' => false];
    }
    
    /**
     * Process authentication triggers
     * 
     * @return void
     */
    public function process_triggers()
    {
        foreach ($this->triggers as $name => $config) {
            if (isset($config['callback']) && is_callable($config['callback'])) {
                call_user_func($config['callback'], $config);
            }
        }
    }
    
    /**
     * Redirect to login page
     * 
     * @param string $back_url URL to return to after login
     * @param string $condition Condition that triggered redirect
     * @return void
     */
    private function redirect_to_login($back_url = '', $condition = '')
    {
        $settings = $this->get_settings();
        $login_page_id = $settings['login_page_id'] ?? '';
        
        if (!$login_page_id) {
            return;
        }
        
        $login_url = get_the_permalink($login_page_id);
        
        if (!empty($back_url)) {
            if ($back_url === 'checkout' && function_exists('wc_get_checkout_url')) {
                $login_url = add_query_arg('backurl', 'checkout', $login_url);
            } else {
                $login_url = add_query_arg('backUrl', urlencode($back_url), $login_url);
            }
        }
        
        // Fire action before redirect
        do_action('voorodak_before_auth_redirect', $login_url, $back_url, $condition);
        
        wp_redirect($login_url);
        exit;
    }
    
    /**
     * Filter authentication requirement
     * 
     * @param bool $required Current requirement status
     * @param array $context Context data
     * @return bool Modified requirement
     */
    public function filter_auth_required($required, $context = [])
    {
        // Allow custom logic to override authentication requirement
        return apply_filters('voorodak_override_auth_required', $required, $context);
    }
}

// Initialize conditional authentication
new Voorodak_Conditional_Auth();
