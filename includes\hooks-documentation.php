<?php
/**
 * Voorodak Plugin Hooks and Filters Documentation
 * 
 * This file documents all available hooks and filters in the Voorodak plugin
 * for developers who want to extend or customize the plugin functionality.
 * 
 * @package Voorodak
 * @version 3.0.2
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * ========================================
 * AUTHENTICATION HOOKS
 * ========================================
 */

/**
 * Fires before username validation during authentication
 * 
 * @param string $username The submitted username
 * @param string $ip_address User's IP address
 */
do_action('voorodak_before_username_submit', $username, $ip_address);

/**
 * Fires before OTP SMS is sent
 * 
 * @param string $mobile Mobile number
 * @param string $otp Generated OTP code
 * @param int|false $user_id User ID if exists, false for new registration
 */
do_action('voorodak_before_otp_sms_send', $mobile, $otp, $user_id);

/**
 * Fires after OTP SMS is sent
 * 
 * @param string $mobile Mobile number
 * @param string $otp Generated OTP code
 * @param int|false $user_id User ID if exists, false for new registration
 * @param bool $sent Whether SMS was sent successfully
 */
do_action('voorodak_after_otp_sms_send', $mobile, $otp, $user_id, $sent);

/**
 * Fires before login validation
 * 
 * @param int $user_id User ID
 * @param WP_User $user User object
 * @param array $additional_data Additional authentication data
 */
do_action('voorodak_before_login_validation', $user_id, $user, $additional_data);

/**
 * Fires before user authentication (setting cookies)
 * 
 * @param int $user_id User ID
 * @param WP_User $user User object
 */
do_action('voorodak_before_authentication', $user_id, $user);

/**
 * Fires after successful authentication
 * 
 * @param int $user_id User ID
 * @param WP_User $user User object
 */
do_action('voorodak_after_authentication', $user_id, $user);

/**
 * Fires when login is blocked by filters
 * 
 * @param int $user_id User ID
 * @param string $reason Reason for blocking
 * @param string $ip_address User's IP address
 */
do_action('voorodak_login_blocked', $user_id, $reason, $ip_address);

/**
 * Fires after successful login (backward compatibility)
 * 
 * @param int $user_id User ID
 */
do_action('voorodak_after_do_login', $user_id);

/**
 * Fires after successful login with enhanced data
 * 
 * @param int $user_id User ID
 * @param WP_User $user User object
 * @param array $login_data Login metadata (ip, timestamp, method, etc.)
 */
do_action('voorodak_login_successful', $user_id, $user, $login_data);

/**
 * ========================================
 * REGISTRATION HOOKS
 * ========================================
 */

/**
 * Fires before registration validation
 * 
 * @param string $username Username (mobile number)
 * @param string|null $first_name First name
 * @param string|null $last_name Last name
 * @param string|null $email Email address
 */
do_action('voorodak_before_registration_validation', $username, $first_name, $last_name, $email);

/**
 * Fires before user creation
 * 
 * @param array $userdata User data array for wp_insert_user
 * @param string $username Original username
 */
do_action('voorodak_before_user_creation', $userdata, $username);

/**
 * Fires when registration fails
 * 
 * @param string $error_message Error message
 * @param string $username Username that failed
 * @param array $userdata User data that was attempted
 */
do_action('voorodak_registration_failed', $error_message, $username, $userdata);

/**
 * Fires after successful registration (backward compatibility)
 * 
 * @param int $user_id New user ID
 */
do_action('voorodak_after_do_register', $user_id);

/**
 * Fires after successful registration with enhanced data
 * 
 * @param int $user_id New user ID
 * @param array $userdata User data used for creation
 * @param array $registration_data Registration metadata
 */
do_action('voorodak_user_registered', $user_id, $userdata, $registration_data);

/**
 * ========================================
 * SECURITY AND RATE LIMITING HOOKS
 * ========================================
 */

/**
 * Fires when rate limit is set
 * 
 * @param string $type Rate limit type (password, otp, general)
 * @param int $count Current attempt count
 * @param string $ip_address User's IP address
 */
do_action('voorodak_rate_limit_set', $type, $count, $ip_address);

/**
 * Fires when rate limit is cleaned
 * 
 * @param string $type Rate limit type
 * @param string $ip_address User's IP address
 */
do_action('voorodak_rate_limit_cleaned', $type, $ip_address);

/**
 * Fires when invalid request is detected
 * 
 * @param string $message Error message
 * @param string $ip_address User's IP address
 * @param string $request_uri Request URI
 */
do_action('voorodak_invalid_request', $message, $ip_address, $request_uri);

/**
 * Fires after AJAX request validation
 * 
 * @param string $rate_limit_type Type of rate limiting applied
 * @param string $ip_address User's IP address
 */
do_action('voorodak_ajax_request_validated', $rate_limit_type, $ip_address);

/**
 * Fires when development bypass is used
 * 
 * @param string $ip_address IP address that used bypass
 */
do_action('voorodak_dev_bypass_used', $ip_address);

/**
 * ========================================
 * SMS GATEWAY HOOKS
 * ========================================
 */

/**
 * Fires before SMS gateway call
 * 
 * @param string $gateway_name Gateway identifier
 * @param string $to Recipient number
 * @param array $otp_data OTP data being sent
 */
do_action('voorodak_before_sms_gateway_call', $gateway_name, $to, $otp_data);

/**
 * Fires after successful SMS gateway call
 * 
 * @param string $gateway_name Gateway identifier
 * @param string $to Recipient number
 * @param mixed $response Gateway response
 * @param bool $success Whether call was successful
 */
do_action('voorodak_after_sms_gateway_call', $gateway_name, $to, $response, $success);

/**
 * Fires when SMS gateway error occurs
 * 
 * @param string $gateway_name Gateway identifier
 * @param string $to Recipient number
 * @param string $error_message Error message
 */
do_action('voorodak_sms_gateway_error', $gateway_name, $to, $error_message);

/**
 * ========================================
 * TOKEN AND RESET HOOKS
 * ========================================
 */

/**
 * Fires when reset token is generated
 * 
 * @param int $user_id User ID
 * @param string $ip_address User's IP address
 */
do_action('voorodak_reset_token_generated', $user_id, $ip_address);

/**
 * Fires when reset token is validated
 * 
 * @param int $user_id User ID
 * @param string $ip_address User's IP address
 */
do_action('voorodak_reset_token_validated', $user_id, $ip_address);

/**
 * Fires when SMS token is verified
 *
 * @param bool $is_valid Whether token is valid
 * @param string $domain Domain being verified
 */
do_action('voorodak_sms_token_verified', $is_valid, $domain);

/**
 * ========================================
 * FILTERS FOR CUSTOMIZATION
 * ========================================
 */

/**
 * Filter rate limit message
 *
 * @param string $message Default rate limit message
 * @param string $type Rate limit type
 * @param int $max_attempts Maximum attempts allowed
 * @param int $timeout Timeout in seconds
 * @return string Modified message
 */
$message = apply_filters('voorodak_rate_limit_message', $message, $type, $max_attempts, $timeout);

/**
 * Filter allowed digits meta keys for security
 *
 * @param array $allowed_keys Default allowed meta keys
 * @return array Modified allowed keys
 */
$allowed_keys = apply_filters('voorodak_allowed_digits_meta_keys', $allowed_keys);

/**
 * Filter user lookup by digits field
 *
 * @param int|false $user_id Found user ID or false
 * @param string $mobile Mobile number searched
 * @param string $meta_key Meta key used for search
 * @return int|false Modified user ID or false
 */
$user_id = apply_filters('voorodak_digits_user_lookup', $user_id, $mobile, $meta_key);

/**
 * Filter custom username validation
 *
 * @param null|string $result Custom validation result (null to use default)
 * @param string $username Username being validated
 * @param string $login_type Login type setting
 * @return null|string Custom validation result
 */
$result = apply_filters('voorodak_custom_username_validation', null, $username, $login_type);

/**
 * Filter pre-login validation
 *
 * @param array $login_data Login data with allow/message keys
 * @param int $user_id User ID attempting login
 * @return array Modified login data
 */
$login_data = apply_filters('voorodak_pre_do_login', $login_data, $user_id);

/**
 * Filter login success message
 *
 * @param string $message Default success message
 * @param int $user_id User ID
 * @param WP_User $user User object
 * @return string Modified message
 */
$message = apply_filters('voorodak_login_success_message', $message, $user_id, $user);

/**
 * Filter login redirect URL
 *
 * @param string $url Default redirect URL (empty)
 * @param int $user_id User ID
 * @param WP_User $user User object
 * @return string Redirect URL
 */
$url = apply_filters('voorodak_login_redirect_url', '', $user_id, $user);

/**
 * Filter registration user data
 *
 * @param array $userdata User data for wp_insert_user
 * @param string $username Original username
 * @param string|null $first_name First name
 * @param string|null $last_name Last name
 * @param string|null $email Email
 * @return array Modified user data
 */
$userdata = apply_filters('voorodak_registration_userdata', $userdata, $username, $first_name, $last_name, $email);

/**
 * Filter OTP generation
 *
 * @param string $otp Generated OTP
 * @param string $mobile Mobile number
 * @param int|false $user_id User ID or false for new registration
 * @return string Modified OTP
 */
$otp = apply_filters('voorodak_generate_otp', $otp, $mobile, $user_id);

/**
 * Filter username submit description
 *
 * @param string $description Default description message
 * @param string $username Username submitted
 * @param int|false $user_id User ID or false
 * @param bool $sent Whether SMS was sent
 * @return string Modified description
 */
$description = apply_filters('voorodak_username_submit_description', $description, $username, $user_id, $sent);

/**
 * Filter development whitelist IPs
 *
 * @param array $whitelist Default whitelist IPs
 * @return array Modified whitelist
 */
$whitelist = apply_filters('voorodak_dev_whitelist', $whitelist);

/**
 * ========================================
 * CONDITIONAL AUTHENTICATION FILTERS
 * ========================================
 */

/**
 * Filter whether authentication is required for specific page
 *
 * @param bool $required Whether authentication is required
 * @param int $post_id Post/Page ID
 * @param WP_Post $post Post object
 * @return bool Modified requirement
 */
$required = apply_filters('voorodak_auth_required_for_page', $required, $post_id, $post);

/**
 * Filter whether authentication is required for specific user role
 *
 * @param bool $required Whether authentication is required
 * @param array $user_roles User roles
 * @param int $user_id User ID
 * @return bool Modified requirement
 */
$required = apply_filters('voorodak_auth_required_for_role', $required, $user_roles, $user_id);

/**
 * Filter authentication method for specific conditions
 *
 * @param string $method Default authentication method
 * @param array $conditions Current conditions (page, user, etc.)
 * @return string Modified authentication method
 */
$method = apply_filters('voorodak_auth_method_for_conditions', $method, $conditions);
