<?php
/**
 * Voorodak Plugin Customization Examples
 * 
 * This file contains practical examples of how to use Voorodak's hooks and filters
 * to customize authentication behavior.
 * 
 * @package Voorodak
 * @version 3.0.2
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * ========================================
 * AUTHENTICATION CUSTOMIZATION EXAMPLES
 * ========================================
 */

/**
 * Example 1: Custom authentication validation
 * Block login for users with specific meta value
 */
add_filter('voorodak_pre_do_login', function($login_data, $user_id) {
    $user_status = get_user_meta($user_id, 'account_status', true);
    
    if ($user_status === 'suspended') {
        $login_data['allow'] = false;
        $login_data['message'] = 'حساب کاربری شما موقتاً مسدود شده است.';
    }
    
    return $login_data;
}, 10, 2);

/**
 * Example 2: Custom OTP generation
 * Use custom OTP format for VIP users
 */
add_filter('voorodak_generate_otp', function($otp, $mobile, $user_id) {
    if ($user_id) {
        $user = get_userdata($user_id);
        if ($user && in_array('vip', $user->roles)) {
            // Generate 8-digit OTP for VIP users
            return str_pad(rand(********, ********), 8, '0', STR_PAD_LEFT);
        }
    }
    
    return $otp;
}, 10, 3);

/**
 * Example 3: Custom redirect after login
 * Redirect different user roles to different pages
 */
add_filter('voorodak_login_redirect_url', function($url, $user_id, $user) {
    if (in_array('administrator', $user->roles)) {
        return admin_url();
    } elseif (in_array('shop_manager', $user->roles)) {
        return admin_url('edit.php?post_type=shop_order');
    } elseif (in_array('customer', $user->roles)) {
        return wc_get_account_endpoint_url('dashboard');
    }
    
    return home_url();
}, 10, 3);

/**
 * Example 4: Log authentication events
 * Keep detailed logs of all authentication activities
 */
add_action('voorodak_login_successful', function($user_id, $user, $login_data) {
    $log_entry = [
        'user_id' => $user_id,
        'username' => $user->user_login,
        'ip_address' => $login_data['ip'],
        'timestamp' => $login_data['timestamp'],
        'method' => $login_data['method'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ];
    
    // Save to custom log table or file
    error_log('Voorodak Login: ' . json_encode($log_entry));
}, 10, 3);

/**
 * Example 5: Custom rate limiting
 * Different rate limits for different user types
 */
add_filter('voorodak_rate_limit_message', function($message, $type, $max_attempts, $timeout) {
    if ($type === 'otp') {
        return 'تعداد درخواست کد تایید بیش از حد مجاز. لطفاً ' . ceil($timeout / 60) . ' دقیقه صبر کنید.';
    }
    
    return $message;
}, 10, 4);

/**
 * ========================================
 * CONDITIONAL AUTHENTICATION EXAMPLES
 * ========================================
 */

/**
 * Example 6: Time-based authentication
 * Require authentication during business hours
 */
add_filter('voorodak_time_restrictions', function($restrictions) {
    return [
        [
            'start' => '09:00',
            'end' => '17:00',
            'days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday']
        ]
    ];
});

/**
 * Example 7: Content-based authentication
 * Protect premium content
 */
add_filter('voorodak_protected_post_types', function($post_types) {
    return array_merge($post_types, ['premium_content', 'vip_post']);
});

/**
 * Example 8: Role-based authentication
 * Different authentication methods for different roles
 */
add_filter('voorodak_auth_method_for_conditions', function($method, $conditions) {
    if (isset($conditions['user_role']) && $conditions['user_role'] === 'administrator') {
        return 'two_factor'; // Require two-factor for admins
    }
    
    return $method;
}, 10, 2);

/**
 * ========================================
 * SMS GATEWAY CUSTOMIZATION EXAMPLES
 * ========================================
 */

/**
 * Example 9: Custom SMS gateway error handling
 * Send email notification when SMS fails
 */
add_action('voorodak_sms_gateway_error', function($gateway_name, $to, $error_message) {
    $admin_email = get_option('admin_email');
    $subject = 'خطای ارسال پیامک در سایت ' . get_bloginfo('name');
    $message = "خطا در ارسال پیامک:\n";
    $message .= "گیت‌وی: {$gateway_name}\n";
    $message .= "شماره: {$to}\n";
    $message .= "خطا: {$error_message}\n";
    $message .= "زمان: " . current_time('Y-m-d H:i:s');
    
    wp_mail($admin_email, $subject, $message);
}, 10, 3);

/**
 * Example 10: SMS content customization
 * Add custom variables to SMS messages
 */
add_filter('voorodak_sms_message_variables', function($variables, $context) {
    $variables['site_name'] = get_bloginfo('name');
    $variables['current_time'] = current_time('H:i');
    $variables['user_ip'] = $_SERVER['REMOTE_ADDR'] ?? '';
    
    return $variables;
}, 10, 2);

/**
 * ========================================
 * WORKFLOW CUSTOMIZATION EXAMPLES
 * ========================================
 */

/**
 * Example 11: Custom authentication workflow
 * Create a workflow for corporate users
 */
add_filter('voorodak_authentication_workflows', function($workflows) {
    $workflows['corporate'] = [
        'name' => 'Corporate Authentication',
        'description' => 'Multi-step authentication for corporate users',
        'steps' => [
            'employee_id' => [
                'title' => 'Enter Employee ID',
                'callback' => 'validate_employee_id',
                'validation' => 'check_employee_database'
            ],
            'department_verification' => [
                'title' => 'Verify Department',
                'callback' => 'verify_department',
                'validation' => 'check_department_access'
            ],
            'manager_approval' => [
                'title' => 'Manager Approval',
                'callback' => 'request_manager_approval',
                'validation' => 'check_approval_status',
                'optional' => true
            ]
        ]
    ];
    
    return $workflows;
});

/**
 * Example 12: Workflow selection based on user agent
 * Use different workflows for mobile vs desktop
 */
add_filter('voorodak_current_workflow', function($workflow, $context) {
    if (wp_is_mobile()) {
        return 'mobile_optimized';
    }
    
    return $workflow;
}, 10, 2);

/**
 * ========================================
 * REGISTRATION CUSTOMIZATION EXAMPLES
 * ========================================
 */

/**
 * Example 13: Custom registration validation
 * Add business logic for registration
 */
add_action('voorodak_before_registration_validation', function($username, $first_name, $last_name, $email) {
    // Check if mobile number is in allowed list
    $allowed_prefixes = ['0912', '0913', '0914', '0915'];
    $prefix = substr($username, 0, 4);
    
    if (!in_array($prefix, $allowed_prefixes)) {
        wp_send_json_error([
            'message' => 'شماره موبایل شما از اپراتورهای مجاز نیست.'
        ]);
    }
});

/**
 * Example 14: Custom user data during registration
 * Add additional fields to user profile
 */
add_filter('voorodak_registration_userdata', function($userdata, $username, $first_name, $last_name, $email) {
    // Add custom fields
    $userdata['description'] = 'Registered via Voorodak SMS system';
    
    return $userdata;
}, 10, 5);

/**
 * Example 15: Post-registration actions
 * Send welcome email and setup user preferences
 */
add_action('voorodak_user_registered', function($user_id, $userdata, $registration_data) {
    // Send welcome email
    $user = get_userdata($user_id);
    $subject = 'خوش آمدید به ' . get_bloginfo('name');
    $message = "سلام {$user->display_name},\n\n";
    $message .= "حساب کاربری شما با موفقیت ایجاد شد.\n";
    $message .= "شماره موبایل: {$registration_data['username']}\n";
    $message .= "زمان ثبت نام: " . date('Y-m-d H:i:s', $registration_data['timestamp']);
    
    if (!empty($user->user_email)) {
        wp_mail($user->user_email, $subject, $message);
    }
    
    // Set default preferences
    update_user_meta($user_id, 'voorodak_sms_notifications', 'enabled');
    update_user_meta($user_id, 'voorodak_preferred_workflow', 'default');
}, 10, 3);

/**
 * ========================================
 * SECURITY ENHANCEMENT EXAMPLES
 * ========================================
 */

/**
 * Example 16: IP-based restrictions
 * Block authentication from specific IP ranges
 */
add_action('voorodak_before_login_validation', function($user_id, $user, $additional_data) {
    $blocked_ips = ['*************', '*********'];
    $user_ip = $_SERVER['REMOTE_ADDR'] ?? '';
    
    if (in_array($user_ip, $blocked_ips)) {
        wp_send_json_error([
            'message' => 'دسترسی از این IP مجاز نیست.'
        ]);
    }
});

/**
 * Example 17: Device fingerprinting
 * Track and validate user devices
 */
add_action('voorodak_login_successful', function($user_id, $user, $login_data) {
    $device_fingerprint = md5(
        ($_SERVER['HTTP_USER_AGENT'] ?? '') . 
        ($_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '') .
        $login_data['ip']
    );
    
    $known_devices = get_user_meta($user_id, 'voorodak_known_devices', true) ?: [];
    
    if (!in_array($device_fingerprint, $known_devices)) {
        // New device detected - send notification
        $known_devices[] = $device_fingerprint;
        update_user_meta($user_id, 'voorodak_known_devices', $known_devices);
        
        // Send security notification
        // Implementation would go here...
    }
}, 10, 3);

/**
 * ========================================
 * INTEGRATION EXAMPLES
 * ========================================
 */

/**
 * Example 18: WooCommerce integration
 * Sync authentication with WooCommerce customer data
 */
add_action('voorodak_after_do_login', function($user_id) {
    if (function_exists('wc_get_customer')) {
        $customer = new WC_Customer($user_id);
        
        // Update last login in WooCommerce
        $customer->update_meta_data('last_login', current_time('mysql'));
        $customer->save();
    }
});

/**
 * Example 19: Third-party CRM integration
 * Sync user data with external CRM system
 */
add_action('voorodak_user_registered', function($user_id, $userdata, $registration_data) {
    // Example API call to CRM system
    $crm_data = [
        'name' => $userdata['display_name'],
        'mobile' => $registration_data['username'],
        'email' => $userdata['user_email'] ?? '',
        'source' => 'website_registration',
        'registration_date' => date('Y-m-d H:i:s', $registration_data['timestamp'])
    ];
    
    // wp_remote_post to CRM API
    // Implementation would go here...
}, 10, 3);
