<?php
/**
 * Voorodak Authentication Workflows System
 * 
 * Provides customizable authentication workflows and processes
 * 
 * @package Voorodak
 * @version 3.0.2
 */

// Exit if accessed directly.
defined('ABSPATH') || exit;

/**
 * Authentication Workflows Manager
 */
class Voorodak_Workflows
{
    use Voorodak_Options;
    
    /**
     * Available workflows
     * 
     * @var array
     */
    private $workflows = [];
    
    /**
     * Current workflow
     * 
     * @var string
     */
    private $current_workflow = 'default';
    
    /**
     * Workflow steps
     * 
     * @var array
     */
    private $workflow_steps = [];
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->init_default_workflows();
        $this->init_hooks();
    }
    
    /**
     * Initialize default workflows
     * 
     * @return void
     */
    private function init_default_workflows()
    {
        // Default SMS workflow
        $this->add_workflow('default', [
            'name' => 'Default SMS Authentication',
            'description' => 'Standard mobile number and OTP authentication',
            'steps' => [
                'username_input' => [
                    'title' => 'Enter Mobile Number',
                    'callback' => [$this, 'step_username_input'],
                    'validation' => [$this, 'validate_username_input']
                ],
                'otp_verification' => [
                    'title' => 'Verify OTP',
                    'callback' => [$this, 'step_otp_verification'],
                    'validation' => [$this, 'validate_otp']
                ],
                'complete_profile' => [
                    'title' => 'Complete Profile',
                    'callback' => [$this, 'step_complete_profile'],
                    'validation' => [$this, 'validate_profile'],
                    'optional' => true
                ]
            ]
        ]);
        
        // Email + SMS workflow
        $this->add_workflow('email_sms', [
            'name' => 'Email + SMS Authentication',
            'description' => 'Email verification followed by SMS OTP',
            'steps' => [
                'email_input' => [
                    'title' => 'Enter Email',
                    'callback' => [$this, 'step_email_input'],
                    'validation' => [$this, 'validate_email_input']
                ],
                'email_verification' => [
                    'title' => 'Verify Email',
                    'callback' => [$this, 'step_email_verification'],
                    'validation' => [$this, 'validate_email_token']
                ],
                'mobile_input' => [
                    'title' => 'Enter Mobile Number',
                    'callback' => [$this, 'step_mobile_input'],
                    'validation' => [$this, 'validate_mobile_input']
                ],
                'sms_verification' => [
                    'title' => 'Verify SMS',
                    'callback' => [$this, 'step_sms_verification'],
                    'validation' => [$this, 'validate_sms_otp']
                ]
            ]
        ]);
        
        // Two-factor workflow
        $this->add_workflow('two_factor', [
            'name' => 'Two-Factor Authentication',
            'description' => 'Password + SMS OTP authentication',
            'steps' => [
                'credentials_input' => [
                    'title' => 'Enter Credentials',
                    'callback' => [$this, 'step_credentials_input'],
                    'validation' => [$this, 'validate_credentials']
                ],
                'two_factor_setup' => [
                    'title' => 'Setup Two-Factor',
                    'callback' => [$this, 'step_two_factor_setup'],
                    'validation' => [$this, 'validate_two_factor_setup'],
                    'optional' => true
                ],
                'two_factor_verify' => [
                    'title' => 'Verify Two-Factor',
                    'callback' => [$this, 'step_two_factor_verify'],
                    'validation' => [$this, 'validate_two_factor_code']
                ]
            ]
        ]);
        
        // Apply filter to allow custom workflows
        $this->workflows = apply_filters('voorodak_authentication_workflows', $this->workflows);
    }
    
    /**
     * Initialize WordPress hooks
     * 
     * @return void
     */
    private function init_hooks()
    {
        add_action('wp_ajax_voorodak_workflow_step', [$this, 'handle_workflow_step']);
        add_action('wp_ajax_nopriv_voorodak_workflow_step', [$this, 'handle_workflow_step']);
        add_filter('voorodak_current_workflow', [$this, 'determine_workflow'], 10, 2);
    }
    
    /**
     * Add authentication workflow
     * 
     * @param string $id Workflow ID
     * @param array $config Workflow configuration
     * @return void
     */
    public function add_workflow($id, $config)
    {
        $this->workflows[$id] = $config;
    }
    
    /**
     * Get workflow by ID
     * 
     * @param string $id Workflow ID
     * @return array|null Workflow configuration
     */
    public function get_workflow($id)
    {
        return $this->workflows[$id] ?? null;
    }
    
    /**
     * Get all workflows
     * 
     * @return array All workflows
     */
    public function get_workflows()
    {
        return $this->workflows;
    }
    
    /**
     * Set current workflow
     * 
     * @param string $workflow_id Workflow ID
     * @return void
     */
    public function set_current_workflow($workflow_id)
    {
        if (isset($this->workflows[$workflow_id])) {
            $this->current_workflow = $workflow_id;
        }
    }
    
    /**
     * Get current workflow
     * 
     * @return string Current workflow ID
     */
    public function get_current_workflow()
    {
        return $this->current_workflow;
    }
    
    /**
     * Determine appropriate workflow based on context
     * 
     * @param string $default_workflow Default workflow
     * @param array $context Context data
     * @return string Workflow ID
     */
    public function determine_workflow($default_workflow, $context = [])
    {
        $settings = $this->get_settings();
        
        // Check for user preference
        if (is_user_logged_in()) {
            $user_workflow = get_user_meta(get_current_user_id(), 'voorodak_preferred_workflow', true);
            if (!empty($user_workflow) && isset($this->workflows[$user_workflow])) {
                return $user_workflow;
            }
        }
        
        // Check for page-specific workflow
        global $post;
        if ($post) {
            $page_workflow = get_post_meta($post->ID, '_voorodak_workflow', true);
            if (!empty($page_workflow) && isset($this->workflows[$page_workflow])) {
                return $page_workflow;
            }
        }
        
        // Check for role-based workflow
        if (isset($context['user_role'])) {
            $role_workflows = $settings['role_workflows'] ?? [];
            if (isset($role_workflows[$context['user_role']])) {
                return $role_workflows[$context['user_role']];
            }
        }
        
        // Check for device-based workflow
        if (wp_is_mobile()) {
            $mobile_workflow = $settings['mobile_workflow'] ?? '';
            if (!empty($mobile_workflow) && isset($this->workflows[$mobile_workflow])) {
                return $mobile_workflow;
            }
        }
        
        return $default_workflow;
    }
    
    /**
     * Start workflow execution
     * 
     * @param string $workflow_id Workflow ID
     * @param array $context Initial context
     * @return array Workflow start result
     */
    public function start_workflow($workflow_id, $context = [])
    {
        $workflow = $this->get_workflow($workflow_id);
        
        if (!$workflow) {
            return [
                'success' => false,
                'message' => 'Workflow not found'
            ];
        }
        
        $this->set_current_workflow($workflow_id);
        
        // Initialize workflow session
        $session_data = [
            'workflow_id' => $workflow_id,
            'current_step' => 0,
            'steps_completed' => [],
            'context' => $context,
            'started_at' => time()
        ];
        
        $this->save_workflow_session($session_data);
        
        // Fire action for workflow start
        do_action('voorodak_workflow_started', $workflow_id, $context);
        
        // Get first step
        $first_step = $this->get_next_step($workflow_id, -1);
        
        return [
            'success' => true,
            'workflow' => $workflow,
            'next_step' => $first_step
        ];
    }
    
    /**
     * Execute workflow step
     * 
     * @param string $workflow_id Workflow ID
     * @param string $step_id Step ID
     * @param array $data Step data
     * @return array Step execution result
     */
    public function execute_step($workflow_id, $step_id, $data = [])
    {
        $workflow = $this->get_workflow($workflow_id);
        
        if (!$workflow || !isset($workflow['steps'][$step_id])) {
            return [
                'success' => false,
                'message' => 'Invalid workflow or step'
            ];
        }
        
        $step = $workflow['steps'][$step_id];
        
        // Fire action before step execution
        do_action('voorodak_before_workflow_step', $workflow_id, $step_id, $data);
        
        // Validate step data
        if (isset($step['validation']) && is_callable($step['validation'])) {
            $validation_result = call_user_func($step['validation'], $data);
            
            if (!$validation_result['valid']) {
                return [
                    'success' => false,
                    'message' => $validation_result['message'],
                    'errors' => $validation_result['errors'] ?? []
                ];
            }
        }
        
        // Execute step callback
        if (isset($step['callback']) && is_callable($step['callback'])) {
            $step_result = call_user_func($step['callback'], $data);
        } else {
            $step_result = ['success' => true];
        }
        
        if ($step_result['success']) {
            // Mark step as completed
            $this->mark_step_completed($workflow_id, $step_id);
            
            // Fire action after successful step
            do_action('voorodak_workflow_step_completed', $workflow_id, $step_id, $data);
            
            // Get next step
            $next_step = $this->get_next_step($workflow_id, $step_id);
            
            if ($next_step) {
                $step_result['next_step'] = $next_step;
            } else {
                // Workflow completed
                $this->complete_workflow($workflow_id);
                $step_result['workflow_completed'] = true;
            }
        }
        
        return $step_result;
    }
    
    /**
     * Get next step in workflow
     * 
     * @param string $workflow_id Workflow ID
     * @param string $current_step_id Current step ID
     * @return array|null Next step configuration
     */
    private function get_next_step($workflow_id, $current_step_id)
    {
        $workflow = $this->get_workflow($workflow_id);
        
        if (!$workflow) {
            return null;
        }
        
        $steps = array_keys($workflow['steps']);
        $current_index = array_search($current_step_id, $steps);
        
        if ($current_index === false) {
            $current_index = -1;
        }
        
        $next_index = $current_index + 1;
        
        if (isset($steps[$next_index])) {
            $next_step_id = $steps[$next_index];
            $next_step = $workflow['steps'][$next_step_id];
            $next_step['id'] = $next_step_id;
            
            return $next_step;
        }
        
        return null;
    }
    
    /**
     * Mark workflow step as completed
     * 
     * @param string $workflow_id Workflow ID
     * @param string $step_id Step ID
     * @return void
     */
    private function mark_step_completed($workflow_id, $step_id)
    {
        $session = $this->get_workflow_session();
        
        if ($session && $session['workflow_id'] === $workflow_id) {
            $session['steps_completed'][] = $step_id;
            $this->save_workflow_session($session);
        }
    }
    
    /**
     * Complete workflow
     * 
     * @param string $workflow_id Workflow ID
     * @return void
     */
    private function complete_workflow($workflow_id)
    {
        $session = $this->get_workflow_session();
        
        if ($session && $session['workflow_id'] === $workflow_id) {
            $session['completed_at'] = time();
            $this->save_workflow_session($session);
            
            // Fire action for workflow completion
            do_action('voorodak_workflow_completed', $workflow_id, $session);
            
            // Clean up session after completion
            $this->clear_workflow_session();
        }
    }
    
    /**
     * Handle AJAX workflow step request
     * 
     * @return void
     */
    public function handle_workflow_step()
    {
        // Verify nonce
        if (!check_ajax_referer('voorodak_security', 'security', false)) {
            wp_send_json_error(['message' => 'Invalid security token']);
        }
        
        $workflow_id = sanitize_text_field($_POST['workflow_id'] ?? '');
        $step_id = sanitize_text_field($_POST['step_id'] ?? '');
        $step_data = $_POST['step_data'] ?? [];
        
        // Sanitize step data
        $step_data = $this->sanitize_step_data($step_data);
        
        $result = $this->execute_step($workflow_id, $step_id, $step_data);
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }
    
    /**
     * Sanitize step data
     * 
     * @param array $data Raw step data
     * @return array Sanitized data
     */
    private function sanitize_step_data($data)
    {
        if (!is_array($data)) {
            return [];
        }
        
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            $key = sanitize_key($key);
            
            if (is_array($value)) {
                $sanitized[$key] = $this->sanitize_step_data($value);
            } else {
                $sanitized[$key] = sanitize_text_field($value);
            }
        }
        
        return $sanitized;
    }
    
    /**
     * Save workflow session data
     * 
     * @param array $session_data Session data
     * @return void
     */
    private function save_workflow_session($session_data)
    {
        set_transient('voorodak_workflow_session_' . $this->get_user_ip(), $session_data, HOUR_IN_SECONDS);
    }
    
    /**
     * Get workflow session data
     * 
     * @return array|false Session data or false
     */
    private function get_workflow_session()
    {
        return get_transient('voorodak_workflow_session_' . $this->get_user_ip());
    }
    
    /**
     * Clear workflow session
     * 
     * @return void
     */
    private function clear_workflow_session()
    {
        delete_transient('voorodak_workflow_session_' . $this->get_user_ip());
    }
    
    // Workflow step implementations would go here...
    // These are placeholder methods that would be implemented based on specific needs
    
    public function step_username_input($data) { return ['success' => true]; }
    public function validate_username_input($data) { return ['valid' => true]; }
    public function step_otp_verification($data) { return ['success' => true]; }
    public function validate_otp($data) { return ['valid' => true]; }
    public function step_complete_profile($data) { return ['success' => true]; }
    public function validate_profile($data) { return ['valid' => true]; }
    public function step_email_input($data) { return ['success' => true]; }
    public function validate_email_input($data) { return ['valid' => true]; }
    public function step_email_verification($data) { return ['success' => true]; }
    public function validate_email_token($data) { return ['valid' => true]; }
    public function step_mobile_input($data) { return ['success' => true]; }
    public function validate_mobile_input($data) { return ['valid' => true]; }
    public function step_sms_verification($data) { return ['success' => true]; }
    public function validate_sms_otp($data) { return ['valid' => true]; }
    public function step_credentials_input($data) { return ['success' => true]; }
    public function validate_credentials($data) { return ['valid' => true]; }
    public function step_two_factor_setup($data) { return ['success' => true]; }
    public function validate_two_factor_setup($data) { return ['valid' => true]; }
    public function step_two_factor_verify($data) { return ['success' => true]; }
    public function validate_two_factor_code($data) { return ['valid' => true]; }
}

// Initialize workflows
new Voorodak_Workflows();
